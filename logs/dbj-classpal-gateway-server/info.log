2025-07-03 08:28:57.804 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final 
2025-07-03 08:28:58.020 [main] INFO  com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848 
2025-07-03 08:28:58.020 [main] INFO  com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000 
2025-07-03 08:28:58.022 [main] INFO  com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0 
2025-07-03 08:28:58.047 [main] INFO  com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels..... 
2025-07-03 08:28:58.047 [main] INFO  com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector] 
2025-07-03 08:28:58.047 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null 
2025-07-03 08:28:58.049 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect properties labels: {} 
2025-07-03 08:28:58.050 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null 
2025-07-03 08:28:58.050 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect jvm labels: {} 
2025-07-03 08:28:58.050 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect env raw labels: null 
2025-07-03 08:28:58.050 [main] INFO  com.alibaba.nacos.common.labels - default nacos collect env labels: {} 
2025-07-03 08:28:58.051 [main] INFO  com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{} 
2025-07-03 08:28:58.060 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success. 
2025-07-03 08:28:58.062 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success. 
2025-07-03 08:28:58.106 [Thread-1] INFO  com.alibaba.nacos.client.auth.ram.identify.CredentialWatcher - null No credential found 
2025-07-03 08:28:58.108 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-07-03 08:28:58.312 [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-07-03 08:28:58.315 [main] INFO  com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0 
2025-07-03 08:28:58.331 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$609/0x0000000401420d00 
2025-07-03 08:28:58.332 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$610/0x0000000401421140 
2025-07-03 08:28:58.332 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1 
2025-07-03 08:28:58.332 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2 
2025-07-03 08:28:58.337 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Try to connect to server on start up, server: {serverIp = '***************', server main port = 18848} 
2025-07-03 08:28:58.351 [main] INFO  com.alibaba.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:*************** ip,serverPort:19848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false} 
2025-07-03 08:28:58.835 [main] INFO  com.alibaba.nacos.common.ability.AbstractAbilityControlManager - Ready to get current node abilities... 
2025-07-03 08:28:58.836 [main] INFO  com.alibaba.nacos.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT] 
2025-07-03 08:28:58.836 [main] INFO  com.alibaba.nacos.common.ability.AbstractAbilityControlManager - Initialize current abilities finish... 
2025-07-03 08:28:58.837 [main] INFO  com.alibaba.nacos.common.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager 
2025-07-03 08:28:58.889 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Success to connect to server [***************:18848] on start up, connectionId = 1751502538832_**************_64186 
2025-07-03 08:28:58.889 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Notify connected event to listeners. 
2025-07-03 08:28:58.890 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Connected,notify listen context... 
2025-07-03 08:28:58.890 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler 
2025-07-03 08:28:58.890 [main] INFO  com.alibaba.nacos.common.remote.client - [3a7f9755-af8d-4959-96e9-49c93b9ef4c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$643/0x00000004015a1588 
2025-07-03 08:28:58.941 [main] INFO  com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false 
2025-07-03 08:28:58.963 [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[dbj-classpal-gateway-server] & group[DEFAULT_GROUP] 
2025-07-03 08:28:58.990 [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[dbj-classpal-gateway-server-dev.yaml] & group[DEFAULT_GROUP] 
2025-07-03 08:28:58.991 [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-dbj-classpal-gateway-server-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-dbj-classpal-gateway-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-dbj-classpal-gateway-server,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-common.yaml,DEFAULT_GROUP'}] 
2025-07-03 08:28:59.001 [main] INFO  com.dbj.gateway.GatewayServer - The following 1 profile is active: "dev" 
2025-07-03 08:29:00.095 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=38eb2470-fcb0-39e3-9ac2-720810dafb05 
2025-07-03 08:29:00.312 [main] WARN  o.s.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead. 
2025-07-03 08:29:00.316 [main] WARN  o.s.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
2025-07-03 08:29:00.321 [main] WARN  o.s.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead. 
2025-07-03 08:29:00.324 [main] WARN  o.s.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
2025-07-03 08:29:00.327 [main] WARN  o.s.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies. 
2025-07-03 08:29:00.882 [main] INFO  org.redisson.Version - Redisson 3.17.6 
2025-07-03 08:29:00.904 [main] ERROR io.netty.resolver.dns.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library 
2025-07-03 08:29:01.027 [redisson-netty-3-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379 
2025-07-03 08:29:01.220 [redisson-netty-3-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379 
2025-07-03 08:29:01.986 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight] 
2025-07-03 08:29:01.987 [main] INFO  org.springframework.cloud.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService] 
2025-07-03 08:29:02.569 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator' 
2025-07-03 08:29:03.410 [main] INFO  org.springframework.boot.web.embedded.netty.NettyWebServer - Netty started on port 62000 
2025-07-03 08:29:03.420 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null 
2025-07-03 08:29:03.421 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null 
2025-07-03 08:29:03.421 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null 
2025-07-03 08:29:03.425 [main] INFO  com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource 
2025-07-03 08:29:03.428 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success. 
2025-07-03 08:29:03.429 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success. 
2025-07-03 08:29:03.513 [main] INFO  com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 53605eda-ee93-460e-977b-2ed7e086aa51 
2025-07-03 08:29:03.514 [main] INFO  com.alibaba.nacos.client.naming - Create naming rpc client for uuid->53605eda-ee93-460e-977b-2ed7e086aa51 
2025-07-03 08:29:03.514 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager 
2025-07-03 08:29:03.514 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService 
2025-07-03 08:29:03.515 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler 
2025-07-03 08:29:03.515 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Try to connect to server on start up, server: {serverIp = '***************', server main port = 18848} 
2025-07-03 08:29:03.515 [main] INFO  com.alibaba.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:*************** ip,serverPort:19848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false} 
2025-07-03 08:29:03.591 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Success to connect to server [***************:18848] on start up, connectionId = 1751502543614_**************_64276 
2025-07-03 08:29:03.591 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler 
2025-07-03 08:29:03.591 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Notify connected event to listeners. 
2025-07-03 08:29:03.591 [main] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$643/0x00000004015a1588 
2025-07-03 08:29:03.591 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming - Grpc connection connect 
2025-07-03 08:29:03.592 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] classpal-cy-dev registering service dbj-classpal-gateway-server with instance Instance{instanceId='null', ip='**************', port=62000, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=[fc00:1234:ffff:0:0:0:0:10], preserved.register.source=SPRING_CLOUD}} 
2025-07-03 08:29:03.605 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP dbj-classpal-gateway-server **************:62000 register finished 
2025-07-03 08:29:03.646 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-app-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:03.646 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-app-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:03.646 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-gateway-server, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:03.646 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-app-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:03.646 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-app-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:03.646 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-gateway-server, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:03.646 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-gateway-server, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:03.646 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-gateway-server, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:03.660 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [{"instanceId":"**************#62002#DEFAULT#DEFAULT_GROUP@@dbj-classpal-app-api-v1","ip":"**************","port":62002,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-app-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.660 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-gateway-server -> [{"instanceId":"**************#62000#DEFAULT#DEFAULT_GROUP@@dbj-classpal-gateway-server","ip":"**************","port":62000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-gateway-server","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.660 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-gateway-server -> [{"instanceId":"**************#62000#DEFAULT#DEFAULT_GROUP@@dbj-classpal-gateway-server","ip":"**************","port":62000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-gateway-server","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.660 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [{"instanceId":"**************#62002#DEFAULT#DEFAULT_GROUP@@dbj-classpal-app-api-v1","ip":"**************","port":62002,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-app-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.667 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [{"instanceId":"**************#62002#DEFAULT#DEFAULT_GROUP@@dbj-classpal-app-api-v1","ip":"**************","port":62002,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-app-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.667 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-gateway-server -> [{"instanceId":"**************#62000#DEFAULT#DEFAULT_GROUP@@dbj-classpal-gateway-server","ip":"**************","port":62000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-gateway-server","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.667 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [{"instanceId":"**************#62002#DEFAULT#DEFAULT_GROUP@@dbj-classpal-app-api-v1","ip":"**************","port":62002,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-app-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.667 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-gateway-server -> [{"instanceId":"**************#62000#DEFAULT#DEFAULT_GROUP@@dbj-classpal-gateway-server","ip":"**************","port":62000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-gateway-server","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:03.681 [boundedElastic-6] WARN  com.alibaba.nacos.client.utils.ConcurrentDiskUtil - lock DEFAULT_GROUP%40%40dbj-classpal-gateway-server conflict;retry time: 1 
2025-07-03 08:29:03.681 [boundedElastic-4] WARN  com.alibaba.nacos.client.utils.ConcurrentDiskUtil - lock DEFAULT_GROUP%40%40dbj-classpal-app-api-v1 conflict;retry time: 1 
2025-07-03 08:29:03.694 [main] INFO  com.alibaba.cloud.nacos.discovery.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler. 
2025-07-03 08:29:03.713 [main] INFO  com.dbj.gateway.GatewayServer - Started GatewayServer in 6.241 seconds (process running for 7.004) 
2025-07-03 08:29:03.716 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis  
2025-07-03 08:29:03.716 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true  
2025-07-03 08:29:03.716 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-classpal-cy-dev-***************_18848] [subscribe] dbj-classpal-gateway-server-dev.yaml+DEFAULT_GROUP+classpal-cy-dev 
2025-07-03 08:29:03.717 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-classpal-cy-dev-***************_18848] [add-listener] ok, tenant=classpal-cy-dev, dataId=dbj-classpal-gateway-server-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-07-03 08:29:03.717 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=dbj-classpal-gateway-server-dev.yaml, group=DEFAULT_GROUP 
2025-07-03 08:29:03.717 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-classpal-cy-dev-***************_18848] [subscribe] dbj-classpal-gateway-server.yaml+DEFAULT_GROUP+classpal-cy-dev 
2025-07-03 08:29:03.717 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-classpal-cy-dev-***************_18848] [add-listener] ok, tenant=classpal-cy-dev, dataId=dbj-classpal-gateway-server.yaml, group=DEFAULT_GROUP, cnt=1 
2025-07-03 08:29:03.717 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=dbj-classpal-gateway-server.yaml, group=DEFAULT_GROUP 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-classpal-cy-dev-***************_18848] [subscribe] common.yaml+DEFAULT_GROUP+classpal-cy-dev 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-classpal-cy-dev-***************_18848] [add-listener] ok, tenant=classpal-cy-dev, dataId=common.yaml, group=DEFAULT_GROUP, cnt=1 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=common.yaml, group=DEFAULT_GROUP 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-classpal-cy-dev-***************_18848] [subscribe] dbj-classpal-gateway-server+DEFAULT_GROUP+classpal-cy-dev 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-classpal-cy-dev-***************_18848] [add-listener] ok, tenant=classpal-cy-dev, dataId=dbj-classpal-gateway-server, group=DEFAULT_GROUP, cnt=1 
2025-07-03 08:29:03.718 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=dbj-classpal-gateway-server, group=DEFAULT_GROUP 
2025-07-03 08:29:04.200 [nacos-grpc-client-executor-***************-7] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 735 
2025-07-03 08:29:04.200 [nacos-grpc-client-executor-***************-7] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 735 
2025-07-03 08:29:04.202 [nacos-grpc-client-executor-***************-8] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 736 
2025-07-03 08:29:04.202 [nacos-grpc-client-executor-***************-8] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 736 
2025-07-03 08:29:33.714 [boundedElastic-2] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-books-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:33.715 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-admin-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:33.714 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-books-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:33.715 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:dbj-classpal-admin-api-v1, group:DEFAULT_GROUP, clusters:  
2025-07-03 08:29:33.715 [boundedElastic-2] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-books-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:33.715 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-books-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:33.715 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-admin-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:33.715 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:dbj-classpal-admin-api-v1, group:DEFAULT_GROUP, cluster:  
2025-07-03 08:29:33.727 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-admin-api-v1 -> [{"instanceId":"**************#62001#DEFAULT#DEFAULT_GROUP@@dbj-classpal-admin-api-v1","ip":"**************","port":62001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-admin-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:33.728 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:33.728 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-admin-api-v1 -> [{"instanceId":"**************#62001#DEFAULT#DEFAULT_GROUP@@dbj-classpal-admin-api-v1","ip":"**************","port":62001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-admin-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:33.728 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 08:29:34.228 [nacos-grpc-client-executor-***************-50] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 739 
2025-07-03 08:29:34.229 [nacos-grpc-client-executor-***************-50] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 739 
2025-07-03 08:29:34.231 [nacos-grpc-client-executor-***************-51] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 740 
2025-07-03 08:29:34.231 [nacos-grpc-client-executor-***************-51] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 740 
2025-07-03 08:35:31.454 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:35:31.455 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:35:31.682 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:31.844 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:35:31.844 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:35:31.868 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.211 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:35:32.211 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:35:32.253 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.699 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 08:35:32.699 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.699 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 08:35:32.699 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.699 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.700 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 08:35:32.699 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 08:35:32.700 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.700 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.700 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:35:32.700 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:35:32.700 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:35:32.742 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.742 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.742 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.742 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.742 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.742 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.886 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:35:32.886 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:35:32.913 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:35:32.945 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:35:32.946 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:35:32.985 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:20.866 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:20.868 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:20.913 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:21.071 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-watermark-template/pageInfo 
2025-07-03 08:39:21.072 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-watermark-template/pageInfo 
2025-07-03 08:39:21.072 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:21.072 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:21.100 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:21.100 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:22.245 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:22.245 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:22.284 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:28.797 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:28.798 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:28.849 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:30.216 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:30.217 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:30.248 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:34.940 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:34.940 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:34.975 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:35.296 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:35.296 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:35.297 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:35.297 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:35.329 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:35.333 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:46.360 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:46.362 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:46.409 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:46.563 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:46.563 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:46.563 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:39:46.563 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/page 
2025-07-03 08:39:46.591 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:46.592 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:54.722 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:54.723 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:54.752 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:56.112 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:56.112 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:56.141 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:39:56.566 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:56.567 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:39:56.603 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:40:38.877 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:40:38.877 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:40:38.911 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:40:39.180 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:40:39.180 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:40:39.182 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:40:39.183 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:40:39.208 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:40:39.210 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:40:49.145 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/detail 
2025-07-03 08:40:49.146 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/detail 
2025-07-03 08:40:49.173 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:41:11.515 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:41:11.516 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:41:11.559 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:28.276 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:55:28.278 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:55:28.331 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:28.391 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:55:28.391 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:55:28.454 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:28.665 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:55:28.665 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:55:28.706 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:31.698 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:55:31.698 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:55:31.698 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:55:31.698 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/user/page 
2025-07-03 08:55:31.742 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:31.744 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:31.914 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:55:31.914 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:55:31.916 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:55:31.916 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:55:31.942 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:55:31.942 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:06.338 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:06.340 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:06.374 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:06.834 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:06.834 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:06.874 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:07.187 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:58:07.187 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:58:07.189 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 08:58:07.189 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 08:58:07.224 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:07.225 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:07.886 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 08:58:07.886 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 08:58:07.927 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.279 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:10.279 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:10.316 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.648 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 08:58:10.648 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.648 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.648 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.648 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.648 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 08:58:10.648 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.649 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 08:58:10.648 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:58:10.648 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 08:58:10.648 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 08:58:10.649 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 08:58:10.685 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.685 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.685 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.685 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.685 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:10.685 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:12.806 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:12.807 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:12.841 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:12.893 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:58:12.893 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 08:58:12.919 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:13.038 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:13.038 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 08:58:13.067 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:13.301 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:58:13.301 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 08:58:13.304 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:58:13.304 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 08:58:13.333 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 08:58:13.333 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:01:55.762 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:01:55.763 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:01:55.806 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:01:56.207 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:01:56.207 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook-shelf/pageInfo 
2025-07-03 09:01:56.207 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:01:56.207 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook-shelf/pageInfo 
2025-07-03 09:01:56.253 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:01:56.253 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:01:58.147 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook-shelf/detail 
2025-07-03 09:01:58.148 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook-shelf/detail 
2025-07-03 09:01:58.182 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:01.927 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 09:02:01.927 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 09:02:01.954 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:12.304 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/pageSysFileImportExcel 
2025-07-03 09:02:12.304 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/pageSysFileImportExcel 
2025-07-03 09:02:12.341 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:12.410 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:02:12.410 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:02:12.445 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:13.022 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/pageSysFileImportExcel 
2025-07-03 09:02:13.023 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/pageSysFileImportExcel 
2025-07-03 09:02:13.076 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:13.189 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:02:13.189 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:02:13.231 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:26.608 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:02:26.609 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:02:26.634 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:26.910 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:02:26.910 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:02:26.913 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-watermark-template/pageInfo 
2025-07-03 09:02:26.914 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-watermark-template/pageInfo 
2025-07-03 09:02:26.959 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:26.963 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:41.119 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:02:41.120 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:02:41.251 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:02:41.251 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:02:41.646 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:41.646 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:41.961 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:41.961 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:42.342 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:42.342 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:42.580 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:42.580 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:02:42.967 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:02:42.967 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:02:57.940 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getRoot 
2025-07-03 09:02:57.940 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getRoot 
2025-07-03 09:02:57.976 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:58.065 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:02:58.065 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:02:58.065 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:02:58.065 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:02:58.091 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:02:58.094 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:05.601 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:05.601 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:05.635 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:06.092 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:06.092 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:06.132 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:07.818 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:07.818 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:07.863 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:08.136 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:03:08.136 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:03:08.225 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:03:08.225 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:03:08.321 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:08.321 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:08.349 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:08.535 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:08.535 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:08.995 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:08.995 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:09.060 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:09.060 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:03:09.110 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:09.298 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:09.298 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:09.562 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:09.562 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:03:09.580 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:09.580 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:09.594 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:03:09.858 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:03:09.858 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:03:15.522 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:03:15.523 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:03:15.642 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:03:15.643 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:03:15.946 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:15.946 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.251 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.251 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.549 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.549 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.695 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:16.695 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:03:17.342 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:03:17.342 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:07.791 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:07.791 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:07.822 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:08.292 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:08.292 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:08.294 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/notice/page 
2025-07-03 09:05:08.294 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/notice/page 
2025-07-03 09:05:08.323 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:08.324 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:09.169 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:09.169 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:09.283 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:05:09.283 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:05:09.636 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:09.636 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:09.896 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:09.896 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:10.199 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:10.199 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:10.527 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:10.527 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:10.948 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:10.948 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:11.435 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:11.435 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:11.464 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:12.697 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:12.697 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:12.697 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:12.698 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:12.739 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:12.739 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:13.591 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:13.591 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:13.623 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:13.664 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:13.664 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:13.698 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:14.103 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:14.103 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:14.139 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:15.691 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:05:15.692 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:05:16.011 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:05:16.012 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:05:16.460 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:16.460 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:16.640 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:16.640 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:17.063 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:17.063 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:17.064 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:17.064 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:17.111 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:17.112 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:17.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:17.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:17.476 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:17.476 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:05:17.491 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:17.491 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:17.493 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:17.493 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:17.519 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:17.524 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:17.789 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:17.789 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:05:19.620 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:19.620 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:19.656 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:19.858 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:19.858 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dept/listSysDept 
2025-07-03 09:05:19.858 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user/pageSysUser 
2025-07-03 09:05:19.858 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dept/listSysDept 
2025-07-03 09:05:19.858 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:19.858 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user/pageSysUser 
2025-07-03 09:05:19.901 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:19.901 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:19.901 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:20.657 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:20.657 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:20.765 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:20.984 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-role/pageSysRole 
2025-07-03 09:05:20.984 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:20.984 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-role/pageSysRole 
2025-07-03 09:05:20.984 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:20.984 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:20.984 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-menu/sysMenuAll 
2025-07-03 09:05:21.053 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:21.053 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:21.053 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:31.250 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:31.251 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:31.372 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:31.902 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:31.902 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:31.902 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:31.902 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:32.004 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:32.004 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:32.794 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:32.794 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:32.878 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:33.341 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:33.341 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:33.377 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:33.543 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:33.543 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:33.544 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:33.544 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:33.576 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:33.576 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:34.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:34.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:34.198 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:34.440 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:34.440 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:34.486 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:35.567 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:35.567 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:35.604 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:36.514 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:36.514 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:36.558 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:36.605 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:36.605 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:36.647 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:36.826 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:36.826 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:36.862 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:39.715 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:39.715 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:39.715 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:39.716 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:05:39.755 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:39.755 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:39.813 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:39.813 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:39.814 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:39.814 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:39.844 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:39.846 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:40.355 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:40.356 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:05:40.389 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:43.430 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:43.430 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:43.468 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:43.623 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/notice/page 
2025-07-03 09:05:43.623 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/notice/page 
2025-07-03 09:05:43.623 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:43.623 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:43.667 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:43.667 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:44.996 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:44.996 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:45.038 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:45.220 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/version/page 
2025-07-03 09:05:45.220 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/version/page 
2025-07-03 09:05:45.225 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:45.225 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:45.225 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:45.225 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:45.249 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:45.255 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:45.255 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:45.811 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/version/page 
2025-07-03 09:05:45.811 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/version/page 
2025-07-03 09:05:45.838 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:45.838 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:45.853 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:45.875 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:46.992 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:46.992 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:46.992 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:46.992 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:47.021 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:47.024 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:47.866 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:47.866 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:47.899 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:48.050 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:48.050 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 09:05:48.085 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:48.205 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:48.205 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:48.238 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:50.817 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:50.817 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:50.818 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/channel/page 
2025-07-03 09:05:50.818 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:50.907 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:50.910 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:50.968 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:50.968 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:50.968 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 09:05:50.968 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 09:05:51.043 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:51.043 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:52.840 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:52.840 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:52.924 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:53.303 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/feature/page 
2025-07-03 09:05:53.304 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/feature/page 
2025-07-03 09:05:53.303 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:53.305 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:53.393 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:53.399 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:54.605 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:54.605 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:05:54.682 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:55.142 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/feature/whitelist/page 
2025-07-03 09:05:55.142 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/feature/whitelist/page 
2025-07-03 09:05:55.143 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:55.143 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:05:55.161 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-config-type/getAllAppConfigTypeList 
2025-07-03 09:05:55.163 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-config-type/getAllAppConfigTypeList 
2025-07-03 09:05:55.163 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-agreement/getAllAppAgreements 
2025-07-03 09:05:55.164 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-agreement/getAllAppAgreements 
2025-07-03 09:05:55.225 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:55.228 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:55.256 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:05:55.256 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:01.550 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:06:01.550 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:06:01.588 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:01.934 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user/pageSysUser 
2025-07-03 09:06:01.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:06:01.934 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user/pageSysUser 
2025-07-03 09:06:01.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:06:01.938 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dept/listSysDept 
2025-07-03 09:06:01.938 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dept/listSysDept 
2025-07-03 09:06:01.968 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:01.968 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:01.968 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:03.907 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:03.907 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:03.952 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:04.483 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:04.483 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:04.520 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:28.259 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:28.259 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:28.297 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:29.846 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:29.846 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:29.895 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:34.474 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:34.474 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:34.507 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:34.953 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:34.953 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:34.990 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:35.191 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.191 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.241 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:35.381 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.381 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.414 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:35.521 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.521 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.552 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:35.681 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.681 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.710 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:35.827 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.827 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:35.851 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.086 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.086 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.113 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.250 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.250 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.287 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.413 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.413 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.470 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.601 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.601 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.650 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.753 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.753 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.801 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:36.911 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.911 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:36.951 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:37.080 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:37.081 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:37.124 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:37.245 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:37.245 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:37.296 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:38.183 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:38.185 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:38.248 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:39.340 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:39.340 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:39.385 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:40.051 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:40.052 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:40.087 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:41.133 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:41.134 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:41.164 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:41.659 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:41.659 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:41.687 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:42.782 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:42.782 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:42.811 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:43.199 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.199 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.245 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:43.608 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.608 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.648 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:43.939 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.939 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-user-help/saveSysUserHelp 
2025-07-03 09:06:43.976 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:06:57.150 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:06:57.151 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:06:57.252 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:06:57.252 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:06:57.794 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:57.794 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:57.941 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:57.942 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:58.140 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:58.141 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:58.433 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:58.433 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:06:58.720 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:06:58.721 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:05.503 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:05.503 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:05.627 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:05.627 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:06.171 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.171 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.467 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.467 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.693 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.693 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.979 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:06.979 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:07.261 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:07.261 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:11.595 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:11.597 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:11.700 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:11.700 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:12.170 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:12.171 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:12.481 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:12.481 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:12.773 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:12.773 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:13.408 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:13.408 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:13.742 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:13.742 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:32.149 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:07:32.149 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:07:32.272 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:32.272 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:07:32.565 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:32.565 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:32.861 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:32.861 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:33.004 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:33.004 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:33.223 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:33.223 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:07:33.744 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:33.745 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:07:42.927 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:07:42.929 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:09:32.724 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:09:32.724 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:09:32.769 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:33.380 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:09:33.380 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:09:33.380 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 09:09:33.380 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:09:33.425 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:33.425 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:34.075 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:09:34.075 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 09:09:34.117 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.373 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:09:35.373 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 09:09:35.412 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.634 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/usedSize 
2025-07-03 09:09:35.634 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/usedSize 
2025-07-03 09:09:35.635 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getRoot 
2025-07-03 09:09:35.635 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getRoot 
2025-07-03 09:09:35.635 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:09:35.635 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 09:09:35.661 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.666 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.666 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.733 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/usedSize 
2025-07-03 09:09:35.733 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:09:35.734 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/usedSize 
2025-07-03 09:09:35.734 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/getMaterialParentsPath 
2025-07-03 09:09:35.771 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:35.771 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:09:36.238 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:09:36.238 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/material/pageInfo 
2025-07-03 09:09:36.274 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 09:11:34.274 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:11:34.276 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:11:40.401 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:11:40.401 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:13:37.463 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:13:37.464 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:13:45.288 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:13:45.288 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:14:14.104 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:14:14.105 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:15:07.250 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:15:07.252 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:16:04.757 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:16:04.758 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:16:22.026 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:16:22.026 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:17:06.338 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:17:06.339 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:19:18.206 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:19:18.206 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:20:48.362 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:20:48.362 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:20:48.385 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:20:48.385 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:20:48.902 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:48.902 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.124 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.124 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.429 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.429 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.731 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:49.731 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:20:50.282 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:20:50.282 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:23:39.784 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:23:39.786 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:23:39.804 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:23:39.805 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:23:40.323 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:40.323 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:40.590 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:40.590 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:40.883 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:40.883 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:41.201 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:41.202 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:23:41.506 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:23:41.506 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:09.425 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:09.426 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:09.454 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:33:09.454 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:33:09.935 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:09.935 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:10.489 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:10.489 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:10.515 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:33:10.515 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:33:10.946 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:10.946 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:11.385 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:11.385 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:11.680 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:11.681 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:12.181 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:12.181 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:33:12.575 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:33:12.575 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:13.148 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:13.149 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:13.203 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:34:13.203 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:34:13.518 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:13.518 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:14.092 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:14.092 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:14.890 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:14.890 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:15.909 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:15.909 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:16.491 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:16.491 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:33.190 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:34:33.191 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:34:33.507 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:33.508 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:33.657 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:33.657 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:33.860 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:33.861 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:34.366 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:34.366 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:34.675 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:34.675 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:34:35.234 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:34:35.234 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:35:54.678 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:35:54.679 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:35:54.701 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:35:54.701 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:35:55.008 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.008 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.283 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.283 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.482 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.482 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.724 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:55.727 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:35:56.020 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:35:56.020 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:20.333 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:20.335 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:20.535 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:37:20.536 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:37:20.925 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:20.925 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.287 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.288 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.530 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.530 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.978 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:21.978 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:22.346 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:22.346 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:34.485 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:34.485 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:34.512 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:34.512 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:34.805 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:34.805 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:35.102 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:35.102 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:35.445 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:35.445 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:36.166 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:36.166 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:36.491 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:36.491 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:41.794 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:37:41.794 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:41.795 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:37:41.795 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:42.289 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:42.289 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:42.591 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:42.591 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:42.908 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:42.908 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:43.204 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:43.204 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:43.667 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:43.667 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:46.984 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:46.984 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:47.034 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:47.034 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:47.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:47.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:47.635 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:47.635 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:47.924 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:47.924 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:48.390 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:48.391 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:48.916 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:48.917 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:54.381 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:54.381 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:37:54.703 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:54.703 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:54.989 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:54.989 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:55.368 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:55.368 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:55.448 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:37:55.448 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:37:55.776 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:55.776 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:37:56.013 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:37:56.013 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:02.554 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:02.555 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:02.575 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:02.575 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:02.887 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:02.887 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:03.230 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:03.231 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:03.786 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:03.786 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:04.107 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:04.107 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:04.418 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:04.418 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:15.720 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:15.722 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:15.820 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:38:15.820 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:38:16.078 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:16.078 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:16.342 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:16.342 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:16.865 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:16.866 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:17.286 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:17.287 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:17.719 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:17.719 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:29.734 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:29.734 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:29.779 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:29.780 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:30.136 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:30.136 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:30.497 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:30.497 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:30.822 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:30.823 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:31.161 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:31.161 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:31.541 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:31.541 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:38.372 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:38.372 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:38:38.504 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:38:38.504 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:38:38.648 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:38.648 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.036 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.036 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.274 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.274 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.718 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:39.718 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:38:40.155 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:38:40.155 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:39:47.764 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:39:47.766 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:39:48.082 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:39:48.082 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:39:48.760 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:48.760 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:49.244 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:49.244 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:49.663 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:49.663 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:50.078 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:50.078 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:39:50.721 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:39:50.721 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:02.676 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:02.676 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:02.678 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:02.678 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:02.901 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:02.901 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:03.450 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:03.450 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:03.719 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:03.719 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:04.261 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:04.262 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:04.624 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:04.624 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:24.265 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:24.267 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:24.548 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:24.548 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:24.915 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:24.915 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.226 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.226 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.658 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.658 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:25.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:26.495 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:26.496 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:29.018 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:29.018 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:29.040 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:41:29.041 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:41:29.395 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:29.395 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:29.645 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:29.645 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:30.358 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:30.358 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:30.669 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:30.669 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:30.989 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:30.989 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:47.237 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:47.239 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:47.263 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:47.264 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:47.470 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:47.470 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:47.744 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:47.744 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:47.890 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:47.891 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:48.437 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:48.437 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:48.800 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:48.800 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:52.038 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:52.038 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:41:52.064 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:52.064 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:41:52.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:52.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:52.625 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:52.625 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:52.998 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:52.998 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:53.383 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:53.383 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:41:53.915 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:41:53.915 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:43:59.595 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:43:59.596 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:43:59.597 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:43:59.597 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:43:59.828 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:43:59.828 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.078 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.078 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.376 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.376 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.600 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.600 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:00.919 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:44:00.919 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:44:37.413 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:44:37.415 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:44:37.437 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:44:37.437 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:44:37.817 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:37.817 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.050 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.051 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.344 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.344 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.637 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.637 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:44:38.930 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:44:38.930 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:15.452 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:15.454 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:15.477 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:46:15.477 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:46:15.757 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:15.757 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:15.986 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:15.986 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:16.290 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:16.291 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:16.778 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:16.778 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:17.091 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:17.091 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:17.561 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:17.561 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:17.590 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:46:17.590 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:46:17.722 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:17.722 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:17.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:17.934 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:18.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:18.161 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:18.731 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:18.731 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:19.241 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:19.241 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:41.250 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:41.250 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:41.276 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:46:41.276 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:46:41.624 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:41.625 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:41.844 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:41.844 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:42.318 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:42.319 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:42.615 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:42.615 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:42.848 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:42.848 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:44.063 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:44.063 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:46:44.089 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:46:44.089 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:46:44.897 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:44.897 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.274 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.274 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.685 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.685 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.975 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:45.975 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:46:46.282 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:46.283 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:46:49.935 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:46:49.935 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:47:25.541 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:47:25.543 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:47:29.403 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:47:29.404 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/landing-data 
2025-07-03 09:47:37.066 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:47:37.066 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:47:37.090 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:47:37.091 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:47:37.392 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:37.392 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:37.703 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:37.703 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:38.254 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:38.254 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:38.480 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:38.480 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:47:38.673 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:47:38.673 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:53:18.504 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:53:18.505 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:53:18.745 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:53:18.746 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:53:19.173 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:19.173 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:19.731 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:19.731 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:20.016 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:20.016 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:20.373 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:20.373 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:20.678 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:53:20.679 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:53:46.642 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:53:46.644 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:53:46.661 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:53:46.662 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:53:47.040 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:47.041 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:47.560 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:47.561 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:47.908 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:47.908 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:48.188 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:48.188 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:53:48.559 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:53:48.563 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:54:52.427 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:54:52.428 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:54:52.448 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:54:52.448 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:54:52.821 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:52.821 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:53.336 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:53.336 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:53.645 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:53.645 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:54.014 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:54.015 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:54:54.405 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:54:54.405 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:55:06.446 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:06.447 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:09.182 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:09.183 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:12.868 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:12.869 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 09:55:20.877 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:55:20.877 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:55:20.900 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:55:20.901 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 09:55:21.350 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:21.351 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:21.676 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:21.676 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:22.299 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:22.300 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:22.825 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:22.825 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:55:23.052 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:55:23.052 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:59:35.707 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:59:35.708 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 09:59:36.073 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:36.073 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:36.441 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:36.441 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:36.694 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:36.694 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:37.000 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:37.000 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 09:59:37.300 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 09:59:37.300 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:00:46.487 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:00:46.487 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:00:46.827 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:46.827 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.054 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.054 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.566 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.566 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.869 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:47.869 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:00:49.000 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:00:49.001 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:05:58.243 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:05:58.245 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:05:58.482 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:05:58.482 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:05:58.785 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:58.785 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.006 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.008 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.699 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.699 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.954 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:05:59.954 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:06:00.242 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:06:00.242 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:06:12.862 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:12.864 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:20.369 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:20.369 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:27.010 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:27.011 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.007 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.008 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.267 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.267 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.563 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:28.563 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:06:59.634 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:06:59.636 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:06:59.675 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:01.620 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:07:01.621 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:07:01.623 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:07:01.623 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:07:01.623 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:07:01.623 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:07:01.623 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:07:01.623 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:07:01.623 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:07:01.623 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:07:01.665 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:01.665 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:01.665 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:01.665 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:01.665 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:02.127 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:07:02.128 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:07:02.161 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:02.199 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:07:02.199 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:07:02.234 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:02.368 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:07:02.368 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:07:02.398 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.583 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:07:05.586 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:07:05.618 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.622 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:07:05.622 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:07:05.622 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:07:05.622 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:07:05.622 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:07:05.622 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:07:05.622 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:07:05.622 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:07:05.753 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.756 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.756 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.756 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.884 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementPage 
2025-07-03 10:07:05.884 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementPage 
2025-07-03 10:07:05.918 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.924 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:07:05.924 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:07:05.926 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:07:05.926 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:07:05.950 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:05.952 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:07:10.000 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:07:10.001 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:07:28.152 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:28.153 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:28.408 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:28.409 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:28.939 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:28.939 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:29.252 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:29.252 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:30.099 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:30.099 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:30.417 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:30.417 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:30.718 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:30.718 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:43.789 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:43.790 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:43.817 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:07:43.817 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:07:44.143 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:44.143 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:44.375 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:44.375 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:44.684 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:44.685 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:45.947 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:45.948 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:46.242 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:46.242 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:53.581 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:53.582 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:53.604 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:53.605 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:07:54.119 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:54.119 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:54.507 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:54.507 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:55.032 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:55.032 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:55.568 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:55.568 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:07:55.810 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:07:55.811 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:05.193 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:05.194 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:05.225 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:08:05.225 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:08:05.516 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:05.516 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:05.941 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:05.941 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:06.567 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:06.568 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:07.128 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:07.129 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:07.385 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:07.385 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:15.481 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:15.483 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:15.571 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:15.571 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:16.529 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:16.530 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:17.180 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:17.180 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:17.879 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:17.880 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:18.412 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:18.412 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:18.747 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:18.747 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:23.939 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:08:23.939 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:23.939 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:08:23.939 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:08:24.184 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:24.184 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:24.390 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:24.390 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:24.929 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:24.929 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:25.215 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:25.215 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:08:25.699 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:08:25.699 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:09:47.535 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:09:47.537 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:09:48.074 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:48.074 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:48.856 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:48.856 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:49.166 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:49.166 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:49.633 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:49.633 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:49.965 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:09:49.966 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:09:52.364 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:09:52.365 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:09:52.466 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:09:52.466 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:09:52.624 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:52.624 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:52.991 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:52.991 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:53.428 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:53.429 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:53.770 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:53.770 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:09:54.089 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:09:54.089 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:10:00.885 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:00.886 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:00.927 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:01.236 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:01.237 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:01.282 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:01.481 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:01.481 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:01.481 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:01.481 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:01.517 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:01.517 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:02.158 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:02.158 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:02.194 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:05.435 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:05.435 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:05.466 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:05.664 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:05.664 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:05.664 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:05.664 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:05.690 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:05.690 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:06.278 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:06.278 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:06.316 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:10.031 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:10.031 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:10:10.070 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:10.396 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/studyCenter/module/page 
2025-07-03 10:10:10.397 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/studyCenter/module/page 
2025-07-03 10:10:10.397 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:10.398 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:10:10.398 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:10:10.398 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:10:10.426 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:10.430 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:10.430 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:14.491 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:14.492 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:14.530 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:15.020 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:15.020 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:15.048 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:26.844 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:26.844 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:10:26.873 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:10:27.404 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:27.404 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:10:27.436 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:11:08.054 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:11:08.055 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:11:08.275 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:08.275 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:09.205 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:09.206 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:09.695 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:09.695 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:10.796 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:10.796 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:11.307 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:11:11.308 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:11:39.274 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:11:39.275 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:11:39.300 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:39.300 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:39.615 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:39.615 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:39.818 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:39.819 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:40.055 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:40.056 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:40.357 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:40.357 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:11:40.655 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:11:40.655 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:11:48.861 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:48.861 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:50.607 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:50.607 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.245 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.245 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.421 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.422 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.721 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:52.721 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.027 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.027 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.331 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.331 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.630 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.631 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.938 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:53.939 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:54.247 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:54.247 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:54.744 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:54.744 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:55.050 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:55.051 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:56.492 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:56.493 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:56.915 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:11:56.916 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:00.947 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:00.948 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:01.563 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:01.564 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:03.158 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:03.158 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:08.240 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:08.241 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:08.813 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:08.814 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:09.200 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:09.201 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:09.639 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:09.639 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:10.143 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:10.143 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:10.438 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:10.438 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:11.098 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:11.098 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:11.645 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:11.645 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.137 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.137 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.428 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.428 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.922 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:12.923 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:13.405 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:12:13.406 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:02.884 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:14:02.885 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:14:03.162 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:03.162 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:03.427 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:03.428 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:03.661 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:03.662 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:04.055 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:04.056 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:04.361 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:04.362 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:14:04.658 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:14:04.658 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:14:23.797 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:23.799 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:32.617 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:32.619 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:34.262 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:34.262 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:34.766 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:34.767 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:35.318 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:35.319 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:35.619 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:35.619 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.124 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.133 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.418 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.418 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.921 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:36.921 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:37.229 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:37.229 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:38.042 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:38.042 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:38.279 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:38.279 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:39.327 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:39.327 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:39.603 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:39.604 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:40.191 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:40.191 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:40.725 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:40.725 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.025 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.025 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.329 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.329 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.868 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:41.868 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:42.166 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:42.166 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:43.288 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:43.288 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:43.586 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:43.586 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:44.299 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:44.300 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.125 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.127 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.437 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.437 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.737 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:45.737 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:46.692 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:46.693 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:47.351 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:14:47.351 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:34.998 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:15:34.999 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:15:35.349 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:35.350 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:35.448 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:35.449 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:35.675 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:35.675 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:35.992 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:35.992 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:36.231 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:36.231 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:15:36.521 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:15:36.521 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:15:43.754 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:43.754 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:50.779 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:50.779 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:54.742 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:54.742 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:55.094 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:55.094 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:56.217 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:56.217 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:56.764 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:56.764 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:57.072 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:57.072 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:57.927 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:57.928 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:58.394 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:58.394 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:58.865 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:15:58.865 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:16:12.583 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:16:12.584 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:24.165 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:17:24.166 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:17:24.190 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:24.191 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:24.447 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:24.448 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:24.665 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:24.665 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:24.973 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:24.973 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:25.497 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:25.498 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:17:25.805 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:17:25.806 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:17:32.506 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:32.508 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:43.532 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:17:43.532 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:19:18.315 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:19:18.317 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:19:18.565 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:19:18.565 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:19:19.286 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:19.286 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:19.639 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:19.639 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:19.913 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:19.913 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:20.371 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:20.372 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:19:20.724 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:19:20.730 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:19:29.624 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:29.626 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:29.668 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:29.971 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 10:19:29.971 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebook/pageInfo 
2025-07-03 10:19:29.972 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 10:19:29.972 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/app-ebooks-config-category/getAllCategory 
2025-07-03 10:19:29.972 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:29.972 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:29.975 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:29.975 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:29.975 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:29.975 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:29.975 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:29.975 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/basic-config/list 
2025-07-03 10:19:30.001 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:30.001 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:30.001 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:30.003 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:30.009 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:30.009 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.372 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:35.372 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:35.400 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.461 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:35.461 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:19:35.461 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:35.461 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/regions/getRegionData 
2025-07-03 10:19:35.463 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:19:35.463 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:19:35.463 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-info/books/info/list 
2025-07-03 10:19:35.463 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/books-category/books/category/list 
2025-07-03 10:19:35.463 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:19:35.463 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementResourceList 
2025-07-03 10:19:35.493 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.493 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.493 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.493 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.497 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:35.601 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementPage 
2025-07-03 10:19:35.601 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/advertisement/getAdvertisementPage 
2025-07-03 10:19:35.634 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:41.196 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:41.196 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:19:41.224 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:41.304 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:41.304 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:19:41.332 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:41.333 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:19:41.333 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumMenus/getAllAlbumMenusTree 
2025-07-03 10:19:41.358 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:19:41.981 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:19:41.981 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/api/albumElements/pageAlbumElements 
2025-07-03 10:19:42.029 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:20:37.957 [nacos-grpc-client-executor-***************-1816] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 845 
2025-07-03 10:20:37.968 [nacos-grpc-client-executor-***************-1816] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:20:37.968 [nacos-grpc-client-executor-***************-1816] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [] 
2025-07-03 10:20:37.970 [nacos-grpc-client-executor-***************-1816] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 845 
2025-07-03 10:20:47.773 [nacos-grpc-client-executor-***************-1818] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 849 
2025-07-03 10:20:47.773 [nacos-grpc-client-executor-***************-1818] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:20:47.773 [nacos-grpc-client-executor-***************-1818] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:20:47.774 [nacos-grpc-client-executor-***************-1818] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 849 
2025-07-03 10:23:02.011 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:02.011 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:23:02.013 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:23:02.013 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:04.629 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:04.630 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:05.549 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:05.549 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:06.323 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:06.325 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:07.443 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:07.443 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:07.749 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:07.749 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:13.460 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:13.461 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:14.222 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:14.223 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:14.370 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:14.370 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:14.908 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:14.908 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:15.190 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:15.190 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:15.226 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:15.347 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:15.348 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:15.566 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:15.566 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:16.254 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:23:16.254 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:23:16.256 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:23:16.256 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:23:16.279 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:16.281 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:16.384 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:16.384 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:17.032 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:17.032 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:17.225 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:17.225 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:17.251 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:17.293 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:23:17.293 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:23:17.323 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:17.448 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:17.448 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:23:17.475 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:20.284 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:23:20.284 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:23:20.285 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:23:20.285 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:23:20.328 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:20.328 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:20.446 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:23:20.446 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:23:20.446 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:23:20.446 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:23:20.471 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:20.471 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:23:40.859 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:40.859 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:40.885 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:23:40.885 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:23:41.221 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:41.221 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:41.409 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:41.409 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:42.050 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:42.050 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:42.860 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:42.860 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:43.346 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:43.346 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:55.689 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:55.690 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:23:55.711 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:55.711 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:23:56.087 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:56.088 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:56.482 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:56.482 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:56.892 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:56.892 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:57.132 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:57.133 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:23:57.930 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:23:57.931 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:24:10.904 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:24:10.905 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/pageForH5 
2025-07-03 10:24:25.334 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:24:25.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:24:25.334 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:24:25.334 [reactor-http-nio-7] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:24:25.612 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:25.612 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.108 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.108 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.590 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.590 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.824 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:26.824 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/config/list 
2025-07-03 10:24:27.065 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:24:27.066 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:29:33.085 [nacos-grpc-client-executor-***************-1954] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 868 
2025-07-03 10:29:33.088 [nacos-grpc-client-executor-***************-1954] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:29:33.088 [nacos-grpc-client-executor-***************-1954] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [] 
2025-07-03 10:29:33.090 [nacos-grpc-client-executor-***************-1954] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 868 
2025-07-03 10:29:45.774 [nacos-grpc-client-executor-***************-1959] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 872 
2025-07-03 10:29:45.774 [nacos-grpc-client-executor-***************-1959] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:29:45.777 [nacos-grpc-client-executor-***************-1959] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:29:45.777 [nacos-grpc-client-executor-***************-1959] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 872 
2025-07-03 10:42:45.111 [nacos-grpc-client-executor-***************-2155] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 882 
2025-07-03 10:42:45.114 [nacos-grpc-client-executor-***************-2155] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:42:45.116 [nacos-grpc-client-executor-***************-2155] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [] 
2025-07-03 10:42:45.120 [nacos-grpc-client-executor-***************-2155] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 882 
2025-07-03 10:43:27.942 [nacos-grpc-client-executor-***************-2164] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 886 
2025-07-03 10:43:27.944 [nacos-grpc-client-executor-***************-2164] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:43:27.944 [nacos-grpc-client-executor-***************-2164] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:43:27.946 [nacos-grpc-client-executor-***************-2164] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 886 
2025-07-03 10:43:29.137 [nacos-grpc-client-executor-***************-2165] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 889 
2025-07-03 10:43:29.137 [nacos-grpc-client-executor-***************-2165] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:43:29.138 [nacos-grpc-client-executor-***************-2165] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [] 
2025-07-03 10:43:29.138 [nacos-grpc-client-executor-***************-2165] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 889 
2025-07-03 10:43:41.852 [nacos-grpc-client-executor-***************-2169] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 892 
2025-07-03 10:43:41.853 [nacos-grpc-client-executor-***************-2169] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:43:41.853 [nacos-grpc-client-executor-***************-2169] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:43:41.853 [nacos-grpc-client-executor-***************-2169] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 892 
2025-07-03 10:45:07.318 [nacos-grpc-client-executor-***************-2192] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 901 
2025-07-03 10:45:07.320 [nacos-grpc-client-executor-***************-2192] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [{"instanceId":"**************#62003#DEFAULT#DEFAULT_GROUP@@dbj-classpal-books-api-v1","ip":"**************","port":62003,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-books-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 10:45:07.320 [nacos-grpc-client-executor-***************-2192] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-books-api-v1 -> [] 
2025-07-03 10:45:07.322 [nacos-grpc-client-executor-***************-2192] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 901 
2025-07-03 10:46:16.649 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:46:16.651 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:47:25.194 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:47:25.194 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:47:25.217 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:47:25.217 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:49:38.845 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:49:38.847 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:49:39.042 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:49:39.042 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:49:51.572 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:49:51.572 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:49:51.572 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-shelf/pageForH5 
2025-07-03 10:49:51.572 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:50:02.866 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:50:02.867 [reactor-http-nio-4] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook-store/store/collections 
2025-07-03 10:50:02.888 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:50:02.888 [reactor-http-nio-5] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/app/v1/h5/api/app-ebook/getAllCategory 
2025-07-03 10:51:30.103 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:51:30.103 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:51:30.142 [reactor-http-nio-6] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:30.289 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:51:30.289 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/login/getLoginUserMenu 
2025-07-03 10:51:30.326 [reactor-http-nio-8] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:30.642 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:51:30.642 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-dict/getSysDictInfoAll 
2025-07-03 10:51:30.666 [reactor-http-nio-9] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:32.520 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:51:32.522 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/evaluation/pageInfo 
2025-07-03 10:51:32.528 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:51:32.528 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-help-items/getSysHelpItemsInfoByPageId 
2025-07-03 10:51:32.558 [reactor-http-nio-10] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:32.558 [reactor-http-nio-1] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:32.797 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:51:32.797 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:51:32.797 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-import-excel/sysFileImportExcelCount 
2025-07-03 10:51:32.797 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - 请求地址:/api/classpal/system/v1/sys-file-export-excel/sysFileExportExcelCount 
2025-07-03 10:51:32.828 [reactor-http-nio-2] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 10:51:32.828 [reactor-http-nio-3] INFO  com.dbj.gateway.filter.AuthFilter - token解析结果:{"phone":"18301403060","tenantId":1,"userId":11,"userName":"豆禾"},user:11 
2025-07-03 11:02:30.192 [Thread-2] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient 
2025-07-03 11:02:30.193 [Thread-5] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher 
2025-07-03 11:02:30.194 [Thread-5] WARN  com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end 
2025-07-03 11:02:30.196 [Thread-2] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end 
2025-07-03 11:02:30.362 [nacos-grpc-client-executor-***************-2460] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Receive server push request, request = NotifySubscriberRequest, requestId = 912 
2025-07-03 11:02:30.363 [nacos-grpc-client-executor-***************-2460] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [{"instanceId":"**************#62002#DEFAULT#DEFAULT_GROUP@@dbj-classpal-app-api-v1","ip":"**************","port":62002,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dbj-classpal-app-api-v1","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[fc00:1234:ffff:0:0:0:0:10]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}] 
2025-07-03 11:02:30.363 [nacos-grpc-client-executor-***************-2460] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@dbj-classpal-app-api-v1 -> [] 
2025-07-03 11:02:30.365 [nacos-grpc-client-executor-***************-2460] INFO  com.alibaba.nacos.common.remote.client - [53605eda-ee93-460e-977b-2ed7e086aa51] Ack server push request, request = NotifySubscriberRequest, requestId = 912 
2025-07-03 11:02:32.311 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now... 
2025-07-03 11:02:32.312 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] classpal-cy-dev deregistering service dbj-classpal-gateway-server with instance: Instance{instanceId='null', ip='**************', port=62000, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}} 
2025-07-03 11:02:32.337 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry - De-registration finished. 
2025-07-03 11:02:32.340 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin 
2025-07-03 11:02:32.342 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin 
2025-07-03 11:02:32.343 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop 
2025-07-03 11:02:32.344 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop 
2025-07-03 11:02:32.345 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin 
2025-07-03 11:02:32.345 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->53605eda-ee93-460e-977b-2ed7e086aa51 
2025-07-03 11:02:32.346 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@52fa250d[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 3064] 
2025-07-03 11:02:32.347 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown 
2025-07-03 11:02:32.347 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@e48e7cf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0] 
2025-07-03 11:02:32.347 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.common.remote.client - Close current connection 1751502543614_**************_64276 
2025-07-03 11:02:32.358 [nacos-grpc-client-executor-***************-2462] INFO  com.alibaba.nacos.common.remote.client.grpc.GrpcClient - [1751502543614_**************_64276]Ignore complete event,isRunning:false,isAbandon=false 
2025-07-03 11:02:32.360 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1a46216c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2472] 
2025-07-03 11:02:32.360 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->53605eda-ee93-460e-977b-2ed7e086aa51 
2025-07-03 11:02:32.360 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped 
2025-07-03 11:02:32.360 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.auth.ram.identify.CredentialService - [null] CredentialService is freed 
2025-07-03 11:02:32.360 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop 
