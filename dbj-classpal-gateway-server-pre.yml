apiVersion: apps/v1
kind: Deployment
metadata:
  name: dbj-classpal-gateway-server
  namespace: {namespace}
spec:
  replicas: 1
  selector:
    matchLabels:
      name: dbj-classpal-gateway-server
  template:
    metadata:
      labels:
        name: dbj-classpal-gateway-server
    spec:
      containers:
        - env:
          - name: Nacos_Server_Addr
            value: "172.23.176.188:8848"
          - name: Nacos_Namespace
            value: "k8s-classpal-pre"
          - name: Nacos_Username
            value: "nacos"
          - name: Nacos_Password
            value: "@Dbj#nacos888"
          - name: XMS_OPTS
            value: 256m
          - name: XMX_OPTS
            value: 1024m
          image: {Image}
          imagePullPolicy: Always
          name: dbj-classpal-gateway-server
          ports:
          - containerPort: 62000
          volumeMounts:
            - name: service-logs
              mountPath: /logs
          resources:
            requests:
              cpu: "20m"
              memory: 600Mi
            limits:
              cpu: "500m"
              memory: 1024Mi

          startupProbe:
            tcpSocket:
              port: 62000
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      
          livenessProbe:
            tcpSocket:
              port: 62000
            initialDelaySeconds: 10
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 3
      
          readinessProbe:
            tcpSocket:
              port: 62000
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1

      imagePullSecrets:
      - name: https-banxueketang-harbor
      restartPolicy: Always
      volumes:
      - name: service-logs
        persistentVolumeClaim:
          claimName: service-logs-pvc-001

---

apiVersion: v1
kind: Service
metadata:
  name: dbj-classpal-gateway-server
  namespace: {namespace}
spec:
  selector:
    name: dbj-classpal-gateway-server
  ports:
  - port: 62000
    targetPort: 62000
