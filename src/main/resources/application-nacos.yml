# Nacos 2.0 专用配置文件
# 解决gRPC连接超时问题
spring:
  cloud:
    nacos:
      # 全局配置
      username: ${Nacos_Username:nacos}
      password: ${Nacos_Password:nacos}
      
      # 服务发现配置
      discovery:
        server-addr: ${Nacos_Server_Addr:**************:8848}
        namespace: ${Nacos_Namespace:k8s-classpal-pre}
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        # gRPC连接配置
        grpc:
          timeout: 15000  # 增加超时时间到15秒
          retry: 5        # 增加重试次数
          keep-alive-time: 30000
          keep-alive-timeout: 5000
          keep-alive-without-calls: true
          max-inbound-message-size: 10485760
        # 心跳配置
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        ip-delete-timeout: 30000
        
      # 配置中心配置  
      config:
        server-addr: ${Nacos_Server_Addr:**************:8848}
        namespace: ${Nacos_Namespace:k8s-classpal-pre}
        file-extension: yaml
        # gRPC连接配置
        grpc:
          timeout: 15000  # 增加超时时间到15秒
          retry: 5        # 增加重试次数
          keep-alive-time: 30000
          keep-alive-timeout: 5000
          keep-alive-without-calls: true
          max-inbound-message-size: 10485760
        # 长轮询配置
        long-poll-timeout: 30000
        config-long-poll-timeout: 30000
        config-retry-time: 3000
        max-retry: 10
        shared-configs:
          - data-id: common.yaml
            refresh: true

# 日志配置，用于调试Nacos连接问题
logging:
  level:
    com.alibaba.nacos: DEBUG
    com.alibaba.cloud.nacos: DEBUG
    com.alibaba.nacos.client.remote: DEBUG
    com.alibaba.nacos.common.remote.client: DEBUG
