spring:
  application:
    name: dbj-classpal-gateway-server
  profiles:
    active: ${profiles_active:dev}
  cloud:
    nacos:
      username: ${Nacos_Username:nacos}
      password: ${Nacos_Password:nacos}
      discovery:
        server-addr: ${Nacos_Server_Addr:192.168.110.209:18848}  #nacos服务地址
        namespace: ${Nacos_Namespace:classpal-cy-dev}
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        # 添加gRPC连接配置
        grpc:
          timeout: 10000
          retry: 3
      config:
        server-addr: ${Nacos_Server_Addr:192.168.110.209:18848}
        file-extension: yaml
        namespace: ${Nacos_Namespace:classpal-cy-dev}
        # 添加gRPC连接配置
        grpc:
          timeout: 10000
          retry: 3
        shared-configs:
          - data-id: common.yaml
            refresh: true
  main:
    allow-bean-definition-overriding: true
    web-application-type: reactive
  logging:
    level:
      org.springframework.cloud.gateway: DEBUG
