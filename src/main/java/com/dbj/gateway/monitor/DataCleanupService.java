//package com.dbj.gateway.monitor;
//
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Set;
//
//@Slf4j
//@Service
//public class DataCleanupService {
//
//    @Resource
//    private RedisTemplate<String, Object> redisTemplate;
//
//    // 每天凌晨2点执行清理
//    @Scheduled(cron = "0 0 2 * * ?")
//    public void cleanupOldData() {
//        try {
//            // 获取30天前的日期
//            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
//            String oldDate = thirtyDaysAgo.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//
//            // 查找需要清理的keys
//            Set<String> keys = redisTemplate.keys("device:activity:" + oldDate + "*");
//
//            if (!keys.isEmpty()) {
//                long count = redisTemplate.delete(keys);
//                log.info("Cleaned up {} old activity records", count);
//            }
//        } catch (Exception e) {
//            log.error("Failed to cleanup old data", e);
//        }
//    }
//}