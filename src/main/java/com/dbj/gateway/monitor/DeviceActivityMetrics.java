package com.dbj.gateway.monitor;

import io.micrometer.core.instrument.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Data
public class DeviceActivityMetrics {
    private final Counter processedActivities;
    private final Counter failedActivities;
    private final Timer processingTime;
    private final Gauge queueSize;
    private final Counter alertsTriggered;

    public DeviceActivityMetrics(MeterRegistry registry) {
        this.processedActivities = Counter.builder("device.activity.processed")
            .description("Number of processed device activities")
            .register(registry);
            
        this.failedActivities = Counter.builder("device.activity.failed")
            .description("Number of failed device activities")
            .register(registry);
            
        this.processingTime = Timer.builder("device.activity.processing.time")
            .description("Time taken to process device activities")
            .register(registry);
            
        this.queueSize = Gauge.builder("device.activity.queue.size", 
            () -> getQueueSize().value())
            .description("Current size of activity queue")
            .register(registry);
            
        this.alertsTriggered = Counter.builder("device.activity.alerts")
            .description("Number of alerts triggered")
            .register(registry);
    }

}