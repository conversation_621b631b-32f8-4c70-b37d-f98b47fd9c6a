package com.dbj.gateway.config;


import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.event.EnableBodyCachingEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class GatewayCommonConfig{

   @Autowired
    private GatewayProperties gatewayProperties;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init(){
        //发布对应路由的EnableBodyCachingEvent事件
        gatewayProperties.getRoutes().forEach(e->{
            EnableBodyCachingEvent enableBodyCachingEvent = new EnableBodyCachingEvent(new Object(), e.getId());
            //发布事件
            applicationContext.publishEvent(enableBodyCachingEvent);
        });
    }
}
