package com.dbj.gateway.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-gateway
 * @className WebFluxEnum
 * @description
 * @date 2025-03-21 10:10
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WebFluxEnum {

    SUCCESS("200", "成功"),
    FAIL_10005("10005", "登录超时, 请重新登录!"),
    FAIL_10006("10006", "您的账号在其他地方登录, 请重新登录或联系管理员!"),
    FAIL_500("500", "非法请求");

    private String code;

    private String value;
}
