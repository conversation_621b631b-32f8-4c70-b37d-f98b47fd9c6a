package com.dbj.gateway.handler;

import com.dbj.classpal.framework.caffeine.help.CaffeineHelper;
import com.dbj.gateway.monitor.DeviceActivityMetrics;
import io.micrometer.core.instrument.Timer;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.time.Duration;

@Slf4j
@Component
public class DeviceActivityHandler {

    private static final String ACTIVITY_CACHE_NAME = "deviceCache";
    
    // 线程池配置常量
    private static final int CORE_POOL_SIZE = 4;
    private static final int MAX_POOL_SIZE = 8;
    private static final int QUEUE_CAPACITY = 1000;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final String THREAD_NAME_PREFIX = "device-activity-";
    
    // 批处理相关配置
    private static final int BATCH_SIZE = 200;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final int MAP_EXPIRE_DAYS = 2;

    // 缓存配置常量
    private static final String CACHE_KEY_FORMAT = "%d:%d:%d"; // appUserId:memberId:deviceId

    // 队列和缓存配置常量
    private static final long POLL_TIMEOUT = 100; // 毫秒，用于批量收集时的等待时间
    private static final int QUEUE_MAX_SIZE = 10000; // 队列最大容量

    // 时间分片存储配置
    private static final String KEY_FORMAT = "device:activity:%d:%d:%s:%s"; // prefix:appUserId:memberId:date:hour
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("HH");

    @Resource
    private DeviceActivityMetrics metrics;

    @Resource
    private CaffeineHelper caffeineHelper;

    @Resource
    private RedissonClient redissonClient;

    @Value("${dbj.metrics.activity.threshold:5}")
    private String ACTIVITY_THRESHOLD;

    private final BlockingQueue<DeviceActivity> activityQueue = new LinkedBlockingQueue<>(QUEUE_MAX_SIZE);

    public DeviceActivityHandler() {
        // 启动处理线程
        ExecutorService executorService = createExecutorService();
        executorService.submit(this::processQueue);
    }

    /**
     * 创建线程池
     */
    private ExecutorService createExecutorService() {
        return new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable runnable) {
                    Thread thread = new Thread(runnable);
                    thread.setName(THREAD_NAME_PREFIX + threadNumber.getAndIncrement());
                    thread.setDaemon(true);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 记录设备活跃
     */
    public void recordDeviceActivity(Integer memberId, Integer appUserId, Integer deviceId) {
        Timer.Sample sample = Timer.start();
        try {
            String cacheKey = String.format(CACHE_KEY_FORMAT, appUserId, memberId, deviceId);
            
            // 从本地缓存获取设备活跃信息
            DeviceActivityInfo localInfo = caffeineHelper.get(ACTIVITY_CACHE_NAME, cacheKey, DeviceActivityInfo.class);
            long now = System.currentTimeMillis();
            
            if (localInfo == null || now - localInfo.getLastActiveTime() >=  TimeUnit.MINUTES.toMillis(Integer.parseInt(ACTIVITY_THRESHOLD))) {
                // 更新本地缓存
                DeviceActivityInfo newInfo = new DeviceActivityInfo(appUserId, memberId, deviceId, now);
                caffeineHelper.put(ACTIVITY_CACHE_NAME, cacheKey, newInfo, TimeUnit.MINUTES.toMillis(Integer.parseInt(ACTIVITY_THRESHOLD)), TimeUnit.MINUTES);
                
                // 加入队列异步处理
                if (!activityQueue.offer(new DeviceActivity(memberId, appUserId, deviceId, LocalDateTime.now()))) {
                    log.warn("Activity queue is full, activity dropped for device: {}", deviceId);
                    metrics.getFailedActivities().increment();
                    return;
                }
                metrics.getProcessedActivities().increment();
            }
        } catch (Exception e) {
            metrics.getFailedActivities().increment();
            log.error("Error recording device activity", e);
        } finally {
            sample.stop(metrics.getProcessingTime());
        }
    }

    private void processQueue() {
        List<DeviceActivity> batch = new ArrayList<>(BATCH_SIZE);
        
        while (!Thread.currentThread().isInterrupted() && Thread.currentThread().getName().startsWith(THREAD_NAME_PREFIX)) {
            try {
                // 使用 take 方法阻塞等待第一条数据
                DeviceActivity activity = activityQueue.take();
                batch.add(activity);
                
                // 继续收集批量数据，但限制等待时间
                long deadline = System.currentTimeMillis() + POLL_TIMEOUT;
                while (batch.size() < BATCH_SIZE && System.currentTimeMillis() < deadline) {
                    activity = activityQueue.poll();
                    if (activity == null) {
                        break;
                    }
                    batch.add(activity);
                }
                
                // 处理收集到的批量数据
                if (!batch.isEmpty()) {
                    processBatchWithRetry(batch);
                    batch.clear();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Activity processor interrupted", e);
                break;
            } catch (Exception e) {
                log.error("Error processing activity queue", e);
                batch.clear();
            }
        }
        
        // 处理剩余的数据
        if (!batch.isEmpty()) {
            try {
                processBatchWithRetry(batch);
            } catch (Exception e) {
                log.error("Error processing final batch", e);
            }
        }
    }

    private void processBatchWithRetry(List<DeviceActivity> activities) {
        int attempts = 0;
        boolean success = false;
        Exception lastException = null;

        while (!success && attempts < MAX_RETRY_ATTEMPTS) {
            try {
                processBatch(activities);
                success = true;
            } catch (Exception e) {
                lastException = e;
                attempts++;
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    try {
                        // 指数退避重试
                        Thread.sleep((long) Math.pow(2, attempts) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        if (!success) {
            log.error("Failed to process batch after {} attempts", attempts, lastException);
            metrics.getFailedActivities().increment();
        }
    }

    private void processBatch(List<DeviceActivity> activities) {
        // 按小时分组数据
        Map<String, Map<String, String>> batchData = new HashMap<>();
        
        activities.forEach(activity -> {
            LocalDateTime activityTime = activity.getTimestamp();
            String date = activityTime.format(DATE_FORMATTER);
            String hour = activityTime.format(HOUR_FORMATTER);
            
            String key = String.format(KEY_FORMAT, 
                activity.getAppUserId(), 
                activity.getMemberId(),
                date,
                hour
            );

            Map<String, String> deviceMap = batchData.computeIfAbsent(key, k -> new HashMap<>());
            deviceMap.put(
                activity.getDeviceId().toString(), 
                String.valueOf(activityTime.atZone(
                    java.time.ZoneId.systemDefault()).toInstant().toEpochMilli())
            );
        });

        // 批量操作，设置更短的过期时间
        for (Map.Entry<String, Map<String, String>> entry : batchData.entrySet()) {
            try {
                RMap<String, String> rMap = redissonClient.getMap(entry.getKey());
                rMap.putAll(entry.getValue());
                // 小时级数据保留较短时间
                rMap.expireAsync(Duration.ofDays(MAP_EXPIRE_DAYS));
            } catch (Exception e) {
                metrics.getFailedActivities().increment();
                log.error("Failed to save batch to Redis for key: {}", entry.getKey(), e);
                throw e;
            }
        }
    }

    @Data
    private static class DeviceActivity {
        private Integer memberId;
        private Integer appUserId;
        private Integer deviceId;
        private LocalDateTime timestamp;

        public DeviceActivity(
             Integer memberId,
            Integer appUserId,
            Integer deviceId,
            LocalDateTime timestamp
        ) {
            this.memberId = memberId;
            this.appUserId = appUserId;
            this.deviceId = deviceId;
            this.timestamp = timestamp;
        }
    }

    @Data
    private static class DeviceActivityInfo {
        private final Integer appUserId;
        private final Integer memberId;
        private final Integer deviceId;
        private final long lastActiveTime;
        private int activeCount;

        public DeviceActivityInfo(Integer appUserId, Integer memberId, Integer deviceId, long lastActiveTime) {
            this.appUserId = appUserId;
            this.memberId = memberId;
            this.deviceId = deviceId;
            this.lastActiveTime = lastActiveTime;
            this.activeCount = 1;
        }
    }
} 