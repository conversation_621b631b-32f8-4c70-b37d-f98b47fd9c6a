package com.dbj.gateway.filter;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.dbj.classpal.framework.constant.Constants;
import com.dbj.classpal.framework.jwt.utils.JwtAppToken;
import com.dbj.classpal.framework.jwt.utils.JwtToken;
import com.dbj.classpal.framework.jwt.utils.entity.JwtAppVO;
import com.dbj.classpal.framework.jwt.utils.entity.JwtAuthVO;
import com.dbj.classpal.framework.jwt.utils.entity.JwtVO;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.dbj.gateway.constants.SecurityConstants;
import com.dbj.gateway.enums.WebFluxEnum;
import com.dbj.gateway.handler.DeviceActivityHandler;
import com.dbj.gateway.utils.ServletUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.text.MessageFormat;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 牙豚
 * @Date 2025/3/31 9:53
 **/
@Slf4j
@Component
public class AuthFilter implements GlobalFilter, Ordered {

    @Autowired
    private RedissonRedisUtils redisUtils;

    @Resource
    private DeviceActivityHandler activityHandler;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();


    @Value("${dbj.security.ignore.urls}")
    private List<String> ignoreUrls;

    @Value("${dbj.security.ifignore.urls}")
    private List<String> ifignoreUrls;

    public final static String SHORT_URL = "dbj:classpal:shortUrl:{0}";

    private String appUrl = "/api/classpal/app";
    private String appH5Url = "/api/classpal/app/v1/h5";
    private String adminUrl  = "/api/classpal/system";
    private String shortUrl  = "/s/";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String url = request.getURI().getPath();

        // 打印所有请求头参数
        log.info("请求地址: {}", url);
        log.info("请求方法: {}", request.getMethod());
        log.info("请求头参数:");
        request.getHeaders().forEach((key, values) -> {
            log.info("  {}: {}", key, String.join(", ", values));
        });

        // 跳过不需要验证的路径
        log.info("请求地址:{}",url);
        if(url.contains(shortUrl)){
            return shorUrl(url,exchange);
        }

        if (ignoreUrls.stream().anyMatch(url::contains)) {
            return chain.filter(exchange);
        }


//        /api/classpal/app
//        /api/classpal/system
        //根据不同的路径前缀
        WebFluxEnum webFluxEnum = null;
        String token = getToken(request);
        if(ifignoreUrls.stream().anyMatch(url::contains)) {
            if(StringUtils.isBlank(token)) {
                return chain.filter(exchange);
            } else {
                if(url.contains(appUrl)) {
                    webFluxEnum = appToken(token,exchange,mutate);
                } else if (url.contains(adminUrl)) {
                    webFluxEnum = adminToken(token,exchange,mutate);
                }
            }
        } else if(url.contains(appUrl)){
            log.info("请求地址: {},对应的的token信息：{}",url,token);
            webFluxEnum = appToken(token,exchange,mutate);
        }else if(url.contains(adminUrl)){
            webFluxEnum = adminToken(token,exchange,mutate);
        }else {
            return ServletUtils.webFluxResponseWriter(exchange.getResponse(), WebFluxEnum.FAIL_500.getValue(), WebFluxEnum.FAIL_500.getCode());
        }

        if(webFluxEnum != null){
            return ServletUtils.webFluxResponseWriter(exchange.getResponse(), webFluxEnum.getValue(), webFluxEnum.getCode());
        }
        return chain.filter(exchange);
    }

    public Mono<Void> shorUrl(String url , ServerWebExchange exchange){

        String[] newPath = url.split("/");
        String longUrl = redisUtils.getValue(MessageFormat.format(SHORT_URL,newPath[newPath.length - 1]));
        if(StringUtils.isEmpty(longUrl)){
            return ServletUtils.webFluxResponseWriter(exchange.getResponse(), WebFluxEnum.FAIL_500.getValue(), WebFluxEnum.FAIL_500.getCode());
        }
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.FOUND); // 302 重定向
        response.getHeaders().setLocation(URI.create(longUrl));
        return response.setComplete();
    }

    public WebFluxEnum appToken(String token , ServerWebExchange exchange, ServerHttpRequest.Builder mutate){
        if (StringUtils.isEmpty(token)) {
            return WebFluxEnum.FAIL_10005;
        }
        JwtAppVO claimsApp = JwtAppToken.getChain(token);
        if (!ObjectUtils.isNotNull(claimsApp)) {
            return WebFluxEnum.FAIL_10005;
        }
        Integer appUserId = claimsApp.getAppUserId();
        Integer memberId = claimsApp.getMemberId();
        Integer deviceId = claimsApp.getDeviceId();
        String source = claimsApp.getSource();
        Integer tenantId = claimsApp.getTenantId();
        String userKey = MessageFormat.format(Constants.USER_APP_TOKEN ,appUserId,memberId,deviceId,source);
        if (!redisUtils.hasKey(userKey)) {
            return WebFluxEnum.FAIL_10005;
        } else if (!token.equals(redisUtils.getValue(userKey))) {
            return WebFluxEnum.FAIL_10006;
        }
        String path = exchange.getRequest().getURI().getPath();
        if (memberId != null && appUserId != null && deviceId != null && shouldRecordActivity(path)) {
            activityHandler.recordDeviceActivity(memberId, appUserId, deviceId);
        }
        redisUtils.setValue(userKey, token, 30, TimeUnit.DAYS);

        // 获取完整的网关地址
        mutate.header(ContextAppUtil.USER_ID_APP_HEADER, appUserId.toString());
        mutate.header(ContextAppUtil.TENANT_ID_HEADER, tenantId.toString());
        mutate.header(ContextAppUtil.MEMBER_ID_APP_HEADER, String.valueOf(memberId));
        mutate.header(ContextAppUtil.SOURCE, String.valueOf(source));
        mutate.header(ContextAppUtil.DEVICE_ID_APP_HEADER, String.valueOf(deviceId));
        exchange.mutate().request(mutate.build()).build();
        return null;
    }


    public WebFluxEnum adminToken(String token , ServerWebExchange exchange, ServerHttpRequest.Builder mutate){
        if (StringUtils.isEmpty(token)) {
            return WebFluxEnum.FAIL_10005;
        }

        JwtVO claims = JwtToken.getChain(token);
        if (!ObjectUtils.isNotNull(claims)) {
            return WebFluxEnum.FAIL_10005;
        }
        Integer userId = claims.getUserId();
        Integer tenantId = claims.getTenantId();
        String applicationAuthKey = MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, claims.getUserId());
        if (!redisUtils.hasKey(Constants.USER_ADMIN_TOKEN + claims.getUserId())) {
            return WebFluxEnum.FAIL_10005;
        } else if (!redisUtils.hasKey(applicationAuthKey)) {
            return WebFluxEnum.FAIL_10005;
        } else if (!token.equals(redisUtils.getValue(Constants.USER_ADMIN_TOKEN + userId))) {
            return WebFluxEnum.FAIL_10006;
        }

        String applicationAuthValue = redisUtils.getValue(applicationAuthKey);
        if (StrUtil.isEmpty(applicationAuthValue)) {
            return WebFluxEnum.FAIL_10005;
        }
        Boolean noLogin = false;
        if(StringUtils.isNotEmpty(applicationAuthValue)){
            JwtAuthVO jwtAuthVO = JSON.parseObject(applicationAuthValue, JwtAuthVO.class);
            if(jwtAuthVO != null){
                if(jwtAuthVO.getIsAdministrators() != null){
                    mutate.header(ContextUtil.IS_ADMINISTRATORS, jwtAuthVO.getIsAdministrators().toString());
                }
//                mutate.header(ContextUtil.DEPTID, jwtAuthVO.getDeptId().toString());
                if(jwtAuthVO.getNoLogin() != null){
                    noLogin = jwtAuthVO.getNoLogin();
                }

            }

        }
        if (noLogin) {
            redisUtils.setValue(Constants.USER_ADMIN_TOKEN + userId, token, 7, TimeUnit.DAYS);
            redisUtils.setValue(MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, userId), applicationAuthValue, 7, TimeUnit.DAYS);
        } else {
            redisUtils.setValue(Constants.USER_ADMIN_TOKEN + userId, token, 1, TimeUnit.DAYS);
            redisUtils.setValue(MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, userId), JSONUtil.toJsonStr(claims), 1, TimeUnit.DAYS);
        }
        // 获取完整的网关地址
        log.info("token解析结果:{},user:{}",JSON.toJSONString(claims),userId);
        mutate.header(ContextUtil.USER_ID_HEADER, userId.toString());
        mutate.header(ContextUtil.TENANT_ID_HEADER, tenantId.toString());
        mutate.header(ContextUtil.IS_NO_LOGIN, noLogin.toString());
        exchange.mutate().request(mutate.build()).build();
        return null;
    }

    /**
     * 获取当前密钥
     */

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange) {
        log.error("[ 没有权限 ]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), "登录超时, 请重新登录! ", "10005");
    }


    /**
     * 被挤下线
     *
     * @param exchange
     * @return
     */
    private Mono<Void> kickOffResponse(ServerWebExchange exchange) {
        log.error("[ 被挤下线 ]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), "您的账号在其他地方登录, 请重新登录或联系管理员! ", "10006");
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(SecurityConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(SecurityConstants.PREFIX)) {
            token = token.replaceFirst(SecurityConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }
    /**
     * 获取请求token
     */
    private String getApplicationAppId(ServerHttpRequest request) {
        return request.getHeaders().getFirst(SecurityConstants.APPLICATION_APP_ID);
    }

    /**
     * 判断是否需要记录活跃
     */
    private boolean shouldRecordActivity(String path) {
        if (ignoreUrls.stream().anyMatch(pattern -> pathMatcher.match(pattern, path))) {
            return false;
        }
        return path.startsWith("/api/classpal/app/") && !path.contains("/api/classpal/app/auth/");
    }

    @Override
    public int getOrder() {
        return -200;
    }
}