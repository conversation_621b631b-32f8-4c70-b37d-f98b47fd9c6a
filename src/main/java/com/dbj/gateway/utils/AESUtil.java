package com.dbj.gateway.utils;

import lombok.extern.slf4j.Slf4j;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

/**
 * AES加解密工具类
 */
@Slf4j
public class AESUtil {
    
    private static final String AES_ALGORITHM = "AES";
    private static final String CIPHER_MODE = "AES/ECB/PKCS5Padding";
    
    /**
     * AES解密
     *
     * @param content 加密内容
     * @param key     密钥
     * @return 解密后的内容
     */
    public static String decrypt(String content, String key) throws Exception {
        try {
            // 1. 创建解密器
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            
            // 2. 初始化解密器
            SecretKey secretKey = generateKey(key);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            // 3. 解密
            byte[] decoded = Base64.getDecoder().decode(content);
            byte[] decrypted = cipher.doFinal(decoded);
            
            // 4. 转换为字符串
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES decryption failed: {}", e.getMessage());
            throw new RuntimeException("解密失败", e);
        }
    }
    
    /**
     * 生成AES密钥
     */
    private static SecretKey generateKey(String key) {
        try {
            // 确保密钥长度为16字节
            byte[] keyBytes = Arrays.copyOf(
                key.getBytes(StandardCharsets.UTF_8), 
                16
            );
            return new SecretKeySpec(keyBytes, AES_ALGORITHM);
        } catch (Exception e) {
            log.error("Generate AES key failed: {}", e.getMessage());
            throw new RuntimeException("生成密钥失败", e);
        }
    }
}