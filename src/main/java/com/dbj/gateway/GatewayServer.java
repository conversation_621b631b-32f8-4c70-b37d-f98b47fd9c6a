/**
 * @Title: GatewayServer.java
 * @Package com.dbj.gateway
 * @date 2023年2月17日 下午5:24:30 <br />
 * @Copyright (c) 2023, Xstc .
 */
package com.dbj.gateway;

import com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.ComponentScan;

/**
 * @ClassName: GatewayServer
 * @date: 2023年2月17日 下午5:24:30
 * <AUTHOR>
 * @Description: TODO(这里用一句话描述这个类的作用)
 * @version 0.0.1
 * @since JDK 1.8
 */
// 如果有注册机（nacos,eureka）什么的可以不用引入
// @EnableDiscoveryClient，只需在配置文件里面配置好就行
@ComponentScan("com.dbj.*")
@EnableDiscoveryClient
@SpringBootApplication(exclude = {Knife4jAutoConfiguration.class})
@RefreshScope
public class GatewayServer {

    public static void main(String[] args) {
        SpringApplication.run(GatewayServer.class, args);
    }
}